# CAN命令进入Bootloader模式说明

## 功能描述
本功能允许通过CAN总线发送特定命令帧，使设备进入bootloader模式进行固件升级。

## 实现原理
1. 监听CAN ID为 `0x400 + 节点地址` 的命令帧
2. 检查数据帧内容是否为特定的bootloader进入命令
3. 在RAM地址 `0xB00710AD` 写入标志位 `0x20004F00`
4. 执行软复位，bootloader会检查该标志位并进入升级模式

## CAN命令格式

### 命令帧格式
- **CAN ID**: `0x400 + RS485_Addr` (节点地址)
- **数据长度**: 8字节
- **数据内容**: `0xB0 0x07 0x10 0xAD 0x20 0x00 0x4F 0x00`

### 数据字节说明
```
字节0-3: 0xB0 0x07 0x10 0xAD  // RAM地址标识 (0xB00710AD)
字节4-7: 0x20 0x00 0x4F 0x00  // 标志位值 (0x20004F00)
```

## 使用示例

### 假设节点地址为1的设备
```
CAN ID: 0x401
数据: B0 07 10 AD 20 00 4F 00
```

### 假设节点地址为5的设备
```
CAN ID: 0x405  
数据: B0 07 10 AD 20 00 4F 00
```

## 安全注意事项
1. 该命令会立即重启设备，请确保设备处于安全状态
2. 建议在设备停机状态下使用该功能
3. 执行命令前请确保bootloader已正确配置

## 调试信息
当接收到有效的bootloader命令时，串口会输出：
```
Bootloader command received, entering bootloader mode...
```

## 修改的文件
- `Core/Src/canopen.c`: 添加了命令处理逻辑和进入bootloader函数
- `Core/Inc/canopen.h`: 添加了函数声明

## 代码实现位置
- 命令解析：`Process_RPDO()` 函数中的 `case 0x400`
- 执行函数：`Enter_Bootloader_Mode()` 函数
